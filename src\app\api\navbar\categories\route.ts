import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '../../../../lib/database-config';


// نوع البيانات للفئة مع الفئات الفرعية
interface CategoryWithSubcategories {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: {
    id: string;
    name: string;
    name_ar: string;
    description?: string;
    description_ar?: string;
    image_url?: string;
    is_active: boolean;
    product_count: number;
  }[];
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 جلب الفئات للـ navbar...');

    // جلب الفئات الرئيسية النشطة
    const categories = await executeQuery<any>(
      `SELECT id, name, name_ar, description, description_ar, image_url, is_active 
       FROM categories 
       WHERE is_active = 1 AND deleted_at IS NULL 
       ORDER BY name_ar ASC`
    );

    console.log(`✅ تم جلب ${categories.length} فئة رئيسية`);

    // جلب الفئات الفرعية مع عدد المنتجات لكل فئة
    const subcategoriesWithCount = await executeQuery<any>(
      `SELECT 
         s.id, 
         s.name, 
         s.name_ar, 
         s.category_id,
         s.description,
         s.description_ar,
         s.image_url,
         s.is_active,
         COUNT(p.id) as product_count
       FROM subcategories s
       LEFT JOIN products p ON s.id = p.subcategory_id AND p.is_active = 1 AND p.deleted_at IS NULL
       WHERE s.is_active = 1 AND s.deleted_at IS NULL
       GROUP BY s.id, s.name, s.name_ar, s.category_id, s.description, s.description_ar, s.image_url, s.is_active
       ORDER BY s.name_ar ASC`
    );

    console.log(`✅ تم جلب ${subcategoriesWithCount.length} فئة فرعية`);

    // ربط الفئات الفرعية بالفئات الرئيسية
    const categoriesWithSubcategories: CategoryWithSubcategories[] = categories.map(category => ({
      ...category,
      subcategories: subcategoriesWithCount.filter(sub => sub.category_id === category.id)
    }));

    // فلترة الفئات التي لها فئات فرعية فقط (اختياري)
    const activeCategories = categoriesWithSubcategories.filter(cat => cat.subcategories.length > 0);

    console.log(`✅ تم إرجاع ${activeCategories.length} فئة مع فئات فرعية`);

    return NextResponse.json({
      success: true,
      data: activeCategories
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الفئات للـ navbar:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
