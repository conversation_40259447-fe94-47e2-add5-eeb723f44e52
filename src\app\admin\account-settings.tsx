import { useState } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../components/admin/AdminLayout';

export default function AccountSettings() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('password');
  
  // بيانات تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  // بيانات تغيير اسم المستخدم
  const [usernameData, setUsernameData] = useState({
    currentPassword: '',
    newUsername: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // تغيير كلمة المرور
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    setError('');

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('كلمة المرور الجديدة وتأكيدها غير متطابقتان');
      setLoading(false);
      return;
    }

    if (passwordData.newPassword.length < 8) {
      setError('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('تم تغيير كلمة المرور بنجاح!');
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setError(data.messageAr || data.message || 'حدث خطأ أثناء تغيير كلمة المرور');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      setError('حدث خطأ أثناء تغيير كلمة المرور');
    } finally {
      setLoading(false);
    }
  };

  // تغيير اسم المستخدم
  const handleUsernameSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    setError('');

    if (usernameData.newUsername.length < 3) {
      setError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/change-username', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: usernameData.currentPassword,
          newUsername: usernameData.newUsername
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('تم تغيير اسم المستخدم بنجاح! سيتم تسجيل خروجك لتسجيل الدخول بالاسم الجديد.');
        setUsernameData({
          currentPassword: '',
          newUsername: ''
        });
        
        // تسجيل خروج بعد 3 ثوان
        setTimeout(async () => {
          await fetch('/api/auth/logout', { method: 'POST', credentials: 'include' });
          localStorage.removeItem('authToken');
          document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
          router.push('/admin/login');
        }, 3000);
      } else {
        setError(data.messageAr || data.message || 'حدث خطأ أثناء تغيير اسم المستخدم');
      }
    } catch (error) {
      console.error('Error changing username:', error);
      setError('حدث خطأ أثناء تغيير اسم المستخدم');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout title="إعدادات الحساب">
      <div className="max-w-4xl mx-auto">
        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 space-x-reverse px-6">
              <button
                onClick={() => setActiveTab('password')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'password'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className="ri-lock-password-line ml-2"></i>
                تغيير كلمة المرور
              </button>
              <button
                onClick={() => setActiveTab('username')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'username'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className="ri-user-line ml-2"></i>
                تغيير اسم المستخدم
              </button>
            </nav>
          </div>

          <div className="p-6">
            {message && (
              <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                {message}
              </div>
            )}

            {error && (
              <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            {/* تغيير كلمة المرور */}
            {activeTab === 'password' && (
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">تغيير كلمة المرور</h3>
                
                <div>
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    كلمة المرور الحالية
                  </label>
                  <input
                    type="password"
                    id="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل كلمة المرور الحالية"
                  />
                </div>

                <div>
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    كلمة المرور الجديدة
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                    required
                    minLength={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل كلمة المرور الجديدة (8 أحرف على الأقل)"
                  />
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                    required
                    minLength={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أعد إدخال كلمة المرور الجديدة"
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {loading ? 'جاري التغيير...' : 'تغيير كلمة المرور'}
                </button>
              </form>
            )}

            {/* تغيير اسم المستخدم */}
            {activeTab === 'username' && (
              <form onSubmit={handleUsernameSubmit} className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">تغيير اسم المستخدم</h3>
                
                <div>
                  <label htmlFor="newUsername" className="block text-sm font-medium text-gray-700 mb-1">
                    اسم المستخدم الجديد
                  </label>
                  <input
                    type="text"
                    id="newUsername"
                    value={usernameData.newUsername}
                    onChange={(e) => setUsernameData({...usernameData, newUsername: e.target.value})}
                    required
                    minLength={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل اسم المستخدم الجديد (3 أحرف على الأقل)"
                  />
                </div>

                <div>
                  <label htmlFor="currentPasswordForUsername" className="block text-sm font-medium text-gray-700 mb-1">
                    كلمة المرور الحالية للتأكيد
                  </label>
                  <input
                    type="password"
                    id="currentPasswordForUsername"
                    value={usernameData.currentPassword}
                    onChange={(e) => setUsernameData({...usernameData, currentPassword: e.target.value})}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل كلمة المرور الحالية للتأكيد"
                  />
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex">
                    <i className="ri-warning-line text-yellow-400 ml-2 mt-0.5"></i>
                    <div>
                      <h4 className="text-sm font-medium text-yellow-800">تنبيه مهم</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        بعد تغيير اسم المستخدم، سيتم تسجيل خروجك تلقائياً وستحتاج لتسجيل الدخول مرة أخرى باسم المستخدم الجديد.
                      </p>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="bg-orange-600 text-white py-2 px-6 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  {loading ? 'جاري التغيير...' : 'تغيير اسم المستخدم'}
                </button>
              </form>
            )}
          </div>
        </div>

        {/* نصائح الأمان */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            <i className="ri-shield-check-line text-green-600 ml-2"></i>
            نصائح الأمان
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">كلمة المرور القوية:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 8 أحرف على الأقل</li>
                <li>• أحرف كبيرة وصغيرة</li>
                <li>• أرقام ورموز خاصة</li>
                <li>• تجنب المعلومات الشخصية</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">اسم المستخدم الآمن:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 3 أحرف على الأقل</li>
                <li>• تجنب الأسماء الشائعة</li>
                <li>• لا تستخدم معلومات شخصية</li>
                <li>• استخدم أحرف وأرقام فقط</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
