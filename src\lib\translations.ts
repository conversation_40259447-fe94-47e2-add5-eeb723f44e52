export const translations = {
  ar: {
    // Navigation
    home: 'الرئيسية',
    products: 'المنتجات',
    categories: 'الفئات',
    about: 'من نحن',
    contact: 'تواصل معنا',
    cart: 'السلة',
    admin: 'لوحة التحكم',
    
    // Common
    loading: 'جاري التحميل...',
    search: 'البحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    price: 'السعر',
    currency: 'ر.س',
    available: 'متوفر',
    unavailable: 'غير متوفر',
    quantity: 'الكمية',
    addToCart: 'إضافة للسلة',
    viewDetails: 'عرض التفاصيل',
    
    // Product Details
    productDetails: {
      title: 'تفاصيل المنتج',
      description: 'الوصف',
      features: 'المميزات',
      specifications: 'المواصفات',
      addToCart: 'إضافة إلى السلة',
      quantity: 'الكمية',
      price: 'السعر',
      available: 'متوفر',
      unavailable: 'غير متوفر',
      notFound: 'المنتج غير موجود',
      notFoundMessage: 'عذراً، لم نتمكن من العثور على المنتج المطلوب',
      backToProducts: 'العودة إلى المنتجات',
      loading: 'جاري تحميل تفاصيل المنتج...',
      technicalSpecs: 'المواصفات التقنية'
    },
    
    // Cart
    cartMessages: {
      addedToCart: 'تم إضافة المنتج إلى السلة بنجاح!',
      empty: 'السلة فارغة',
      total: 'المجموع',
      checkout: 'إتمام الطلب'
    },
    
    // WhatsApp
    whatsapp: {
      message: 'مرحباً، أريد الاستفسار عن هذا المنتج:'
    },

    // Cart page
    cartTitle: 'سلة التسوق',
    cartSubtitle: 'راجع المنتجات المختارة واطلب عرض سعر',
    cartEmpty: 'السلة فارغة',
    cartEmptyMessage: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
    browseProducts: 'تصفح المنتجات',
    requestQuote: 'طلب عرض سعر',
    customerInfo: 'معلومات العميل',
    orderSent: 'تم إرسال طلبك بنجاح! سنتواصل معك قريباً.',

    // Contact page
    contactTitle: 'تواصل معنا',
    contactSubtitle: 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك',
    formTitle: 'أرسل لنا رسالة',
    fullName: 'الاسم الكامل',
    emailAddress: 'البريد الإلكتروني',
    phoneNumber: 'رقم الهاتف',
    subject: 'الموضوع',
    message: 'الرسالة',
    sendMessage: 'إرسال الرسالة',
    sending: 'جاري الإرسال...',
    successMessage: 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.',
    errorMessage: 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.',
    contactInfo: 'معلومات التواصل',
    address: 'العنوان',
    workingHours: 'ساعات العمل',
    location: 'موقعنا',

    // About page
    aboutTitle: 'من نحن',
    aboutSubtitle: 'تعرف على شركة دروب هاجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم',
    vision: 'رؤيتنا',
    mission: 'مهمتنا',
    values: 'قيمنا',
    team: 'فريقنا'
  },
  
  en: {
    // Navigation
    home: 'Home',
    products: 'Products',
    categories: 'Categories',
    about: 'About Us',
    contact: 'Contact',
    cart: 'Cart',
    admin: 'Admin Panel',
    
    // Common
    loading: 'Loading...',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    price: 'Price',
    currency: 'SAR',
    available: 'Available',
    unavailable: 'Unavailable',
    quantity: 'Quantity',
    addToCart: 'Add to Cart',
    viewDetails: 'View Details',
    
    // Product Details
    productDetails: {
      title: 'Product Details',
      description: 'Description',
      features: 'Features',
      specifications: 'Specifications',
      addToCart: 'Add to Cart',
      quantity: 'Quantity',
      price: 'Price',
      available: 'Available',
      unavailable: 'Unavailable',
      notFound: 'Product Not Found',
      notFoundMessage: 'Sorry, we could not find the requested product',
      backToProducts: 'Back to Products',
      loading: 'Loading product details...',
      technicalSpecs: 'Technical Specifications'
    },
    
    // Cart
    cartMessages: {
      addedToCart: 'Product added to cart successfully!',
      empty: 'Cart is empty',
      total: 'Total',
      checkout: 'Checkout'
    },
    
    // WhatsApp
    whatsapp: {
      message: 'Hello, I would like to inquire about this product:'
    },

    // Cart page
    cartTitle: 'Shopping Cart',
    cartSubtitle: 'Review selected products and request a quote',
    cartEmpty: 'Cart is Empty',
    cartEmptyMessage: 'You haven\'t added any products to your cart yet',
    browseProducts: 'Browse Products',
    requestQuote: 'Request Quote',
    customerInfo: 'Customer Information',
    orderSent: 'Your request has been sent successfully! We will contact you soon.',

    // Contact page
    contactTitle: 'Contact Us',
    contactSubtitle: 'We are here to help you with all your inquiries and needs',
    formTitle: 'Send us a message',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    phoneNumber: 'Phone Number',
    subject: 'Subject',
    message: 'Message',
    sendMessage: 'Send Message',
    sending: 'Sending...',
    successMessage: 'Your message has been sent successfully! We will contact you soon.',
    errorMessage: 'An error occurred while sending the message. Please try again.',
    contactInfo: 'Contact Information',
    address: 'Address',
    workingHours: 'Working Hours',
    location: 'Our Location',

    // About page
    aboutTitle: 'About Us',
    aboutSubtitle: 'Learn about Droob Hajer, specialized in providing the best hotel and restaurant equipment',
    vision: 'Our Vision',
    mission: 'Our Mission',
    values: 'Our Values',
    team: 'Our Team'
  }
} as const;

export type TranslationKey = keyof typeof translations.ar;
export type NestedTranslationKey = 
  | 'productDetails.title'
  | 'productDetails.description'
  | 'productDetails.features'
  | 'productDetails.specifications'
  | 'productDetails.addToCart'
  | 'productDetails.quantity'
  | 'productDetails.price'
  | 'productDetails.available'
  | 'productDetails.unavailable'
  | 'productDetails.notFound'
  | 'productDetails.notFoundMessage'
  | 'productDetails.backToProducts'
  | 'productDetails.loading'
  | 'productDetails.technicalSpecs'
  | 'cartMessages.addedToCart'
  | 'cartMessages.empty'
  | 'cartMessages.total'
  | 'cartMessages.checkout'
  | 'whatsapp.message';

export function getTranslation(
  locale: 'ar' | 'en',
  key: TranslationKey | NestedTranslationKey
): string {
  const keys = key.split('.');
  let value: any = translations[locale];
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  return value || key;
}
