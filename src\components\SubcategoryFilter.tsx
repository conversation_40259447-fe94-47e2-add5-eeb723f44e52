'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { Subcategory } from '../types/mysql-database';

interface SubcategoryFilterProps {
  locale: Locale;
  subcategories: Subcategory[];
  selectedSubcategory: string;
  onSubcategoryChange: (subcategoryId: string) => void;
  productCounts?: { [key: string]: number };
}

const SubcategoryFilter: React.FC<SubcategoryFilterProps> = ({ 
  locale, 
  subcategories, 
  selectedSubcategory, 
  onSubcategoryChange,
  productCounts = {}
}) => {
  if (subcategories.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {locale === 'ar' ? 'الفئات الفرعية' : 'Subcategories'}
      </label>

      <div className="space-y-2">
        {/* خيار "جميع الفئات الفرعية" */}
        <button
          onClick={() => onSubcategoryChange('all')}
          className={`w-full p-2 rounded-md border text-sm transition-colors duration-200 flex items-center justify-between ${
            selectedSubcategory === 'all'
              ? 'border-blue-500 bg-blue-50 text-blue-700'
              : 'border-gray-300 bg-white text-gray-700 hover:border-blue-300'
          }`}
        >
          <span className="font-medium">
            {locale === 'ar' ? 'جميع الفئات' : 'All Categories'}
          </span>
          {productCounts['all'] && (
            <span className={`text-xs px-2 py-1 rounded-full ${
              selectedSubcategory === 'all'
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-600'
            }`}>
              {productCounts['all']}
            </span>
          )}
        </button>

        {/* الفئات الفرعية */}
        {subcategories.map((subcategory) => (
          <button
            key={subcategory.id}
            onClick={() => onSubcategoryChange(subcategory.id)}
            className={`w-full p-2 rounded-md border text-sm transition-colors duration-200 flex items-center justify-between ${
              selectedSubcategory === subcategory.id
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-300 bg-white text-gray-700 hover:border-blue-300'
            }`}
          >
            <span className="font-medium text-sm">
              {locale === 'ar' ? subcategory.nameAr : subcategory.name}
            </span>
            {productCounts[subcategory.id] && (
              <span className={`text-xs px-2 py-1 rounded-full ${
                selectedSubcategory === subcategory.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {productCounts[subcategory.id]}
              </span>
            )}
          </button>
        ))}
      </div>


    </div>
  );
};

export default SubcategoryFilter;
